<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

// Test the wildcard search through the web interface
$searchTerm = "14096/3/2024*";

echo "<!DOCTYPE html>";
echo "<html lang='ro'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Test Wildcard Search</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";

echo "<h2>Testing Wildcard Case Number Search: '{$searchTerm}'</h2>";

// Simulate the search process from index.php
$searchType = detectSearchType($searchTerm);
echo "<p><strong>Detected search type:</strong> {$searchType}</p>";

if ($searchType === 'numarDosar') {
    // Normalize case number
    $caseNumberInfo = normalizeCaseNumber($searchTerm);
    
    echo "<h3>Case Number Normalization</h3>";
    echo "<ul>";
    echo "<li>Original: '{$caseNumberInfo['original']}'</li>";
    echo "<li>Normalized: '{$caseNumberInfo['normalized']}'</li>";
    echo "<li>Has wildcard: " . ($caseNumberInfo['hasWildcard'] ? 'YES' : 'NO') . "</li>";
    echo "<li>Has suffix: " . ($caseNumberInfo['hasSuffix'] ? 'YES' : 'NO') . "</li>";
    echo "</ul>";
    
    // Perform the search
    $searchParams = [
        'numarDosar' => $caseNumberInfo['normalized'],
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null,
        '_caseNumberInfo' => $caseNumberInfo
    ];
    
    try {
        $results = cautaDosare($searchParams);
        
        echo "<h3>Search Results</h3>";
        echo "<p>Found " . count($results) . " cases</p>";
        
        if (count($results) > 0) {
            echo "<div class='table-responsive'>";
            echo "<table class='table table-striped'>";
            echo "<thead>";
            echo "<tr>";
            echo "<th>Număr Dosar</th>";
            echo "<th>Instanță</th>";
            echo "<th>Data Dosar</th>";
            echo "<th>Obiect</th>";
            echo "<th>Materie Juridică</th>";
            echo "<th>Stadiu Procesual</th>";
            echo "</tr>";
            echo "</thead>";
            echo "<tbody>";
            
            foreach ($results as $dosar) {
                $numarDosar = $dosar->numar ?? '';
                $institutie = $dosar->institutie ?? '';
                $dataDosar = $dosar->data ?? '';
                $obiect = $dosar->obiect ?? '';
                $categorie = $dosar->categorieCazNume ?? '';
                $stadiu = $dosar->stadiuProcesualNume ?? '';
                
                // Format date for display
                $dataFormatted = '';
                if (!empty($dataDosar)) {
                    try {
                        $date = new DateTime($dataDosar);
                        $dataFormatted = $date->format('d.m.Y');
                    } catch (Exception $e) {
                        $dataFormatted = $dataDosar;
                    }
                }
                
                // Get institution name
                $instituteName = '';
                if (!empty($institutie)) {
                    $institutii = getInstitutii();
                    $instituteName = $institutii[$institutie] ?? $institutie;
                }
                
                echo "<tr>";
                echo "<td>" . htmlspecialchars($numarDosar) . "</td>";
                echo "<td>" . htmlspecialchars($instituteName) . "</td>";
                echo "<td>" . htmlspecialchars($dataFormatted) . "</td>";
                echo "<td>" . htmlspecialchars($obiect) . "</td>";
                echo "<td>" . htmlspecialchars($categorie) . "</td>";
                echo "<td>" . htmlspecialchars($stadiu) . "</td>";
                echo "</tr>";
                
                // Check if this matches the expected case
                if ($numarDosar === '14096/3/2024' && $institutie === 'TribunalulBUCURESTI') {
                    echo "<tr class='table-success'>";
                    echo "<td colspan='6'>";
                    echo "<strong>✅ This matches the expected case!</strong><br>";
                    echo "Expected details:<br>";
                    echo "- Instanță: Tribunalul BUCUREȘTI<br>";
                    echo "- Număr dosar: 14096/3/2024*<br>";
                    echo "- Dată dosar: 03.10.2024<br>";
                    echo "- Obiect: procedura insolvenței – societăți pe acțiuni Rejudecare<br>";
                    echo "- Materie juridică: Faliment<br>";
                    echo "- Stadiu procesual: Fond<br>";
                    echo "<br><strong>Actual details:</strong><br>";
                    echo "- Instanță: " . htmlspecialchars($instituteName) . "<br>";
                    echo "- Număr dosar: " . htmlspecialchars($numarDosar) . "<br>";
                    echo "- Dată dosar: " . htmlspecialchars($dataFormatted) . "<br>";
                    echo "- Obiect: " . htmlspecialchars($obiect) . "<br>";
                    echo "- Materie juridică: " . htmlspecialchars($categorie) . "<br>";
                    echo "- Stadiu procesual: " . htmlspecialchars($stadiu) . "<br>";
                    echo "</td>";
                    echo "</tr>";
                }
            }
            
            echo "</tbody>";
            echo "</table>";
            echo "</div>";
        } else {
            echo "<div class='alert alert-warning'>No results found</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
} else {
    echo "<div class='alert alert-info'>Search term was not detected as a case number</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
