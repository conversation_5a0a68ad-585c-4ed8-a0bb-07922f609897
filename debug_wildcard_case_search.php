<?php
require_once 'config/config.php';
require_once 'src/Services/DosarService.php';

use App\Services\DosarService;

// Test the specific wildcard case number search
$searchTerm = "14096/3/2024*";
$expectedCaseNumber = "14096/3/2024";
$expectedInstitution = "TribunalulBUCURESTI";

echo "=== DEBUGGING WILDCARD CASE NUMBER SEARCH ===" . PHP_EOL;
echo "Search term: '{$searchTerm}'" . PHP_EOL;
echo "Expected case: {$expectedCaseNumber} at {$expectedInstitution}" . PHP_EOL;
echo PHP_EOL;

try {
    $dosarService = new DosarService();
    
    // Step 1: Test normalization function
    echo "=== STEP 1: Testing Case Number Normalization ===" . PHP_EOL;
    
    // Use reflection to access private method
    $reflection = new ReflectionClass($dosarService);
    $normalizeMethod = $reflection->getMethod('normalizeCaseNumber');
    $normalizeMethod->setAccessible(true);
    
    $caseNumberInfo = $normalizeMethod->invoke($dosarService, $searchTerm);
    
    echo "Original: '{$caseNumberInfo['original']}'" . PHP_EOL;
    echo "Normalized: '{$caseNumberInfo['normalized']}'" . PHP_EOL;
    echo "Has wildcard: " . ($caseNumberInfo['hasWildcard'] ? 'YES' : 'NO') . PHP_EOL;
    echo "Has suffix: " . ($caseNumberInfo['hasSuffix'] ? 'YES' : 'NO') . PHP_EOL;
    echo PHP_EOL;
    
    // Step 2: Test direct SOAP API call with normalized number
    echo "=== STEP 2: Direct SOAP API Call ===" . PHP_EOL;
    
    $executeSoapMethod = $reflection->getMethod('executeSoapCallWithRetry');
    $executeSoapMethod->setAccessible(true);
    
    $searchParams = [
        'numarDosar' => $caseNumberInfo['normalized'],
        'institutie' => null, // Search all institutions
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    echo "SOAP Parameters: " . json_encode($searchParams, JSON_UNESCAPED_UNICODE) . PHP_EOL;
    
    $rawResponse = $executeSoapMethod->invoke($dosarService, 'CautareDosare2', $searchParams, "Debug wildcard search");
    
    if (!$rawResponse || !isset($rawResponse->CautareDosare2Result)) {
        echo "❌ No SOAP response received" . PHP_EOL;
        exit;
    }
    
    $dosare = [];
    if (isset($rawResponse->CautareDosare2Result->Dosar)) {
        $rawDosare = $rawResponse->CautareDosare2Result->Dosar;
        if (!is_array($rawDosare)) {
            $rawDosare = [$rawDosare];
        }
        $dosare = $rawDosare;
    }
    
    echo "Raw SOAP API returned " . count($dosare) . " cases" . PHP_EOL;
    echo PHP_EOL;
    
    // Step 3: Analyze each case from SOAP response
    echo "=== STEP 3: Analyzing SOAP Response Cases ===" . PHP_EOL;
    
    $foundTargetCase = false;
    foreach ($dosare as $index => $dosar) {
        $caseNumber = $dosar->numar ?? '';
        $institution = $dosar->institutie ?? '';
        $object = $dosar->obiect ?? '';
        $category = $dosar->categorieCazNume ?? '';
        $stage = $dosar->stadiuProcesualNume ?? '';
        $date = $dosar->data ?? '';
        
        echo "Case " . ($index + 1) . ":" . PHP_EOL;
        echo "  Number: '{$caseNumber}'" . PHP_EOL;
        echo "  Institution: '{$institution}'" . PHP_EOL;
        echo "  Object: '{$object}'" . PHP_EOL;
        echo "  Category: '{$category}'" . PHP_EOL;
        echo "  Stage: '{$stage}'" . PHP_EOL;
        echo "  Date: '{$date}'" . PHP_EOL;
        
        // Check if this is our target case
        if ($caseNumber === $expectedCaseNumber && $institution === $expectedInstitution) {
            $foundTargetCase = true;
            echo "  ✅ THIS IS THE TARGET CASE!" . PHP_EOL;
        }
        
        echo PHP_EOL;
    }
    
    // Step 4: Test client-side filtering
    echo "=== STEP 4: Testing Client-Side Wildcard Filtering ===" . PHP_EOL;
    
    $filterMethod = $reflection->getMethod('filterResultsByCaseNumberPattern');
    $filterMethod->setAccessible(true);
    
    $filteredResults = $filterMethod->invoke($dosarService, $dosare, $caseNumberInfo);
    
    echo "Original results: " . count($dosare) . PHP_EOL;
    echo "Filtered results: " . count($filteredResults) . PHP_EOL;
    echo PHP_EOL;
    
    if (count($filteredResults) > 0) {
        echo "=== FILTERED RESULTS ===" . PHP_EOL;
        foreach ($filteredResults as $index => $dosar) {
            $caseNumber = $dosar->numar ?? '';
            $institution = $dosar->institutie ?? '';
            $object = $dosar->obiect ?? '';
            
            echo "Filtered Case " . ($index + 1) . ":" . PHP_EOL;
            echo "  Number: '{$caseNumber}'" . PHP_EOL;
            echo "  Institution: '{$institution}'" . PHP_EOL;
            echo "  Object: '{$object}'" . PHP_EOL;
            
            // Check if this matches our expected case
            if ($caseNumber === $expectedCaseNumber && $institution === $expectedInstitution) {
                echo "  ✅ MATCHES EXPECTED CASE!" . PHP_EOL;
            }
            
            echo PHP_EOL;
        }
    } else {
        echo "❌ No results after filtering" . PHP_EOL;
    }
    
    // Step 5: Test the full search method
    echo "=== STEP 5: Testing Full Search Method ===" . PHP_EOL;
    
    $searchMethod = $reflection->getMethod('cautareDupaNumarDosar');
    $searchMethod->setAccessible(true);
    
    $fullResults = $searchMethod->invoke($dosarService, $searchTerm);
    
    echo "Full search method returned: " . count($fullResults) . " results" . PHP_EOL;
    
    if (count($fullResults) > 0) {
        foreach ($fullResults as $index => $dosar) {
            $caseNumber = $dosar->numar ?? '';
            $institution = $dosar->institutie ?? '';
            
            echo "Full Result " . ($index + 1) . ": '{$caseNumber}' at '{$institution}'" . PHP_EOL;
            
            if ($caseNumber === $expectedCaseNumber && $institution === $expectedInstitution) {
                echo "  ✅ FOUND TARGET CASE IN FULL RESULTS!" . PHP_EOL;
            }
        }
    }
    
    // Summary
    echo PHP_EOL . "=== SUMMARY ===" . PHP_EOL;
    echo "Target case found in SOAP response: " . ($foundTargetCase ? "YES" : "NO") . PHP_EOL;
    echo "Cases after filtering: " . count($filteredResults) . PHP_EOL;
    echo "Cases from full search: " . count($fullResults) . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
    echo "Stack trace: " . $e->getTraceAsString() . PHP_EOL;
}
?>
