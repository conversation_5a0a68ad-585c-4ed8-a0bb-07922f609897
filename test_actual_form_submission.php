<?php
// Test the actual form submission process
$_POST['bulkSearchTerms'] = "14096/3/2024*";
$_SERVER['REQUEST_METHOD'] = 'POST';

// Capture the output from index.php
ob_start();
include 'index.php';
$output = ob_get_clean();

// Extract the results table from the output
$dom = new DOMDocument();
@$dom->loadHTML($output);
$xpath = new DOMXPath($dom);

echo "<!DOCTYPE html>";
echo "<html lang='ro'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Test Actual Form Submission</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";

echo "<h2>Testing Actual Form Submission with '14096/3/2024*'</h2>";

// Look for the results table
$tables = $xpath->query('//table[contains(@class, "table")]');
$foundResults = false;

if ($tables->length > 0) {
    echo "<h3>Search Results Found</h3>";
    
    foreach ($tables as $table) {
        $rows = $xpath->query('.//tr', $table);
        
        if ($rows->length > 1) { // More than just header
            $foundResults = true;
            echo "<div class='table-responsive'>";
            echo $dom->saveHTML($table);
            echo "</div>";
            
            // Check for our specific case
            $targetFound = false;
            foreach ($rows as $row) {
                $cells = $xpath->query('.//td', $row);
                if ($cells->length > 0) {
                    $caseNumber = trim($cells->item(0)->textContent ?? '');
                    $institution = trim($cells->item(1)->textContent ?? '');
                    
                    if ($caseNumber === '14096/3/2024' && strpos($institution, 'BUCUREȘTI') !== false) {
                        $targetFound = true;
                        break;
                    }
                }
            }
            
            if ($targetFound) {
                echo "<div class='alert alert-success mt-3'>";
                echo "<strong>✅ SUCCESS!</strong> Found the expected case '14096/3/2024' from Tribunalul BUCUREȘTI in the results.";
                echo "</div>";
            } else {
                echo "<div class='alert alert-warning mt-3'>";
                echo "<strong>⚠️ WARNING!</strong> The expected case was not found in the results table.";
                echo "</div>";
            }
            break;
        }
    }
}

if (!$foundResults) {
    echo "<div class='alert alert-danger'>";
    echo "<strong>❌ ERROR!</strong> No results table found in the output.";
    echo "</div>";
    
    // Look for error messages
    $alerts = $xpath->query('//div[contains(@class, "alert")]');
    if ($alerts->length > 0) {
        echo "<h3>Error Messages Found</h3>";
        foreach ($alerts as $alert) {
            echo "<div class='alert alert-info'>";
            echo htmlspecialchars($alert->textContent);
            echo "</div>";
        }
    }
    
    // Show a portion of the raw output for debugging
    echo "<h3>Raw Output Sample (first 2000 characters)</h3>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 400px; overflow-y: auto;'>";
    echo htmlspecialchars(substr($output, 0, 2000));
    echo "</pre>";
}

// Check if there are any search criteria displayed
$searchCriteria = $xpath->query('//div[contains(@class, "search-criteria")]');
if ($searchCriteria->length > 0) {
    echo "<h3>Search Criteria</h3>";
    foreach ($searchCriteria as $criteria) {
        echo "<div class='alert alert-info'>";
        echo htmlspecialchars($criteria->textContent);
        echo "</div>";
    }
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
