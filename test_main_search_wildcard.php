<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

// Simulate a POST request to test the main search functionality
$_POST['searchTerms'] = "14096/3/2024*";
$_POST['searchType'] = 'auto'; // Auto-detect

echo "<!DOCTYPE html>";
echo "<html lang='ro'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Test Main Search Wildcard</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";

echo "<h2>Testing Main Search Interface with Wildcard: '14096/3/2024*'</h2>";

// Process the search exactly like index.php does
$searchTerms = trim($_POST['searchTerms'] ?? '');
$searchType = $_POST['searchType'] ?? 'auto';

echo "<h3>Search Parameters</h3>";
echo "<ul>";
echo "<li>Search Terms: '{$searchTerms}'</li>";
echo "<li>Search Type: '{$searchType}'</li>";
echo "</ul>";

if (!empty($searchTerms)) {
    $terms = explodeSearchTerms($searchTerms);
    echo "<h3>Exploded Terms</h3>";
    echo "<ul>";
    foreach ($terms as $index => $term) {
        echo "<li>Term " . ($index + 1) . ": '{$term}'</li>";
    }
    echo "</ul>";
    
    $allResults = [];
    $searchCriteria = [];
    
    foreach ($terms as $term) {
        $term = trim($term);
        if (empty($term)) continue;
        
        // Auto-detect search type for this term
        $detectedType = detectSearchType($term);
        echo "<h4>Processing term: '{$term}' (detected as: {$detectedType})</h4>";
        
        $searchParams = [
            'institutie' => null,
            'dataStart' => null,
            'dataStop' => null,
            'dataUltimaModificareStart' => null,
            'dataUltimaModificareStop' => null
        ];
        
        // Set the appropriate parameter based on detected type
        switch ($detectedType) {
            case 'numarDosar':
                $caseNumberInfo = normalizeCaseNumber($term);
                $searchParams['numarDosar'] = $caseNumberInfo['normalized'];
                $searchParams['_caseNumberInfo'] = $caseNumberInfo;
                
                echo "<h5>Case Number Info</h5>";
                echo "<ul>";
                echo "<li>Original: '{$caseNumberInfo['original']}'</li>";
                echo "<li>Normalized: '{$caseNumberInfo['normalized']}'</li>";
                echo "<li>Has wildcard: " . ($caseNumberInfo['hasWildcard'] ? 'YES' : 'NO') . "</li>";
                echo "<li>Has suffix: " . ($caseNumberInfo['hasSuffix'] ? 'YES' : 'NO') . "</li>";
                echo "</ul>";
                break;
            case 'obiectDosar':
                $searchParams['obiectDosar'] = $term;
                break;
            case 'numeParte':
            default:
                $searchParams['numeParte'] = $term;
                break;
        }
        
        try {
            $termResults = cautaDosare($searchParams);
            
            // Apply client-side wildcard filtering for case numbers
            if (!empty($termResults) && $detectedType === 'numarDosar' && isset($searchParams['_caseNumberInfo'])) {
                $caseNumberInfo = $searchParams['_caseNumberInfo'];
                if ($caseNumberInfo['hasWildcard'] || $caseNumberInfo['hasSuffix']) {
                    echo "<p><strong>Applying client-side wildcard filtering...</strong></p>";
                    $originalCount = count($termResults);
                    $termResults = filterResultsByCaseNumberPattern($termResults, $caseNumberInfo);
                    $filteredCount = count($termResults);
                    echo "<p>Pattern filtering reduced results from {$originalCount} to {$filteredCount}</p>";
                }
            }
            
            echo "<h5>Results for term '{$term}'</h5>";
            echo "<p>Found " . count($termResults) . " cases</p>";
            
            if (count($termResults) > 0) {
                echo "<div class='table-responsive'>";
                echo "<table class='table table-striped table-sm'>";
                echo "<thead>";
                echo "<tr>";
                echo "<th>Număr</th>";
                echo "<th>Instanță</th>";
                echo "<th>Data</th>";
                echo "<th>Obiect</th>";
                echo "<th>Stadiu</th>";
                echo "</tr>";
                echo "</thead>";
                echo "<tbody>";
                
                foreach ($termResults as $dosar) {
                    $numarDosar = $dosar->numar ?? '';
                    $institutie = $dosar->institutie ?? '';
                    $dataDosar = $dosar->data ?? '';
                    $obiect = $dosar->obiect ?? '';
                    $stadiu = $dosar->stadiuProcesualNume ?? '';
                    
                    // Format date
                    $dataFormatted = '';
                    if (!empty($dataDosar)) {
                        try {
                            $date = new DateTime($dataDosar);
                            $dataFormatted = $date->format('d.m.Y');
                        } catch (Exception $e) {
                            $dataFormatted = $dataDosar;
                        }
                    }
                    
                    // Get institution name
                    $instituteName = '';
                    if (!empty($institutie)) {
                        $institutii = getInstitutii();
                        $instituteName = $institutii[$institutie] ?? $institutie;
                    }
                    
                    $rowClass = '';
                    if ($numarDosar === '14096/3/2024' && $institutie === 'TribunalulBUCURESTI') {
                        $rowClass = 'table-success';
                    }
                    
                    echo "<tr class='{$rowClass}'>";
                    echo "<td>" . htmlspecialchars($numarDosar) . "</td>";
                    echo "<td>" . htmlspecialchars($instituteName) . "</td>";
                    echo "<td>" . htmlspecialchars($dataFormatted) . "</td>";
                    echo "<td>" . htmlspecialchars(substr($obiect, 0, 50)) . "...</td>";
                    echo "<td>" . htmlspecialchars($stadiu) . "</td>";
                    echo "</tr>";
                }
                
                echo "</tbody>";
                echo "</table>";
                echo "</div>";
            }
            
            $allResults = array_merge($allResults, $termResults);
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>Error searching for term '{$term}': " . htmlspecialchars($e->getMessage()) . "</div>";
        }
    }
    
    echo "<h3>Final Summary</h3>";
    echo "<p>Total results across all terms: " . count($allResults) . "</p>";
    
    // Check if we found the expected case
    $foundExpectedCase = false;
    foreach ($allResults as $dosar) {
        if (($dosar->numar ?? '') === '14096/3/2024' && ($dosar->institutie ?? '') === 'TribunalulBUCURESTI') {
            $foundExpectedCase = true;
            break;
        }
    }
    
    if ($foundExpectedCase) {
        echo "<div class='alert alert-success'>";
        echo "<strong>✅ SUCCESS!</strong> The expected case '14096/3/2024' from 'TribunalulBUCURESTI' was found in the results.";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<strong>⚠️ WARNING!</strong> The expected case '14096/3/2024' from 'TribunalulBUCURESTI' was NOT found in the results.";
        echo "</div>";
    }
    
} else {
    echo "<div class='alert alert-info'>No search terms provided</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
