<?php
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

use App\Services\DosarService;

// Simulate a POST request to test the main search functionality
$_POST['bulkSearchTerms'] = "14096/3/2024*";
$_POST['searchType'] = 'auto'; // Auto-detect

echo "<!DOCTYPE html>";
echo "<html lang='ro'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Test Main Search Wildcard</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-4'>";

echo "<h2>Testing Main Search Interface with Wildcard: '14096/3/2024*'</h2>";

// Add the required functions from index.php
/**
 * Detectează automat tipul de căutare pe baza conținutului termenului
 */
function detectSearchType($term) {
    $cleanTerm = trim($term, '"\'');

    // Pattern pentru numărul de dosar cu asterisk wildcard
    if (preg_match('/^\d+\/\d+(?:\/\d+)?\*$/', $cleanTerm)) {
        return 'numarDosar';
    }

    // Pattern pentru numărul de dosar cu sufixe suplimentare
    if (preg_match('/^\d+\/\d+(?:\/\d+)?\/[a-zA-Z0-9]+$/', $cleanTerm)) {
        return 'numarDosar';
    }

    // Pattern standard pentru numărul de dosar
    if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $cleanTerm)) {
        return 'numarDosar';
    }

    // Pattern cu prefixe (nr., dosar, număr)
    if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i', $cleanTerm)) {
        return 'numarDosar';
    }

    return 'numeParte';
}

/**
 * Normalizează numărul de dosar pentru căutarea SOAP API
 */
function normalizeCaseNumber($caseNumber) {
    $cleanNumber = trim($caseNumber, '"\'');
    $hasWildcard = false;
    $hasSuffix = false;
    $originalSuffix = '';
    $normalizedNumber = $cleanNumber;

    // Detectează și elimină asterisk wildcard
    if (preg_match('/^(.+)\*$/', $cleanNumber, $matches)) {
        $hasWildcard = true;
        $normalizedNumber = $matches[1];
    }

    // Detectează sufixe suplimentare
    if (preg_match('/^(\d+\/\d+(?:\/\d+)?)\/([a-zA-Z][a-zA-Z0-9]*|[0-9]*[a-zA-Z][a-zA-Z0-9]*)$/', $normalizedNumber, $matches)) {
        $hasSuffix = true;
        $originalSuffix = $matches[2];
        $normalizedNumber = $matches[1];
    }

    // Elimină prefixele
    if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(.+)$/i', $normalizedNumber, $matches)) {
        $normalizedNumber = $matches[1];
    }

    return [
        'normalized' => $normalizedNumber,
        'original' => $cleanNumber,
        'hasWildcard' => $hasWildcard,
        'hasSuffix' => $hasSuffix,
        'suffix' => $originalSuffix
    ];
}

/**
 * Filtrează rezultatele după pattern-ul numărului de dosar
 */
function filterResultsByCaseNumberPattern($results, $caseNumberInfo) {
    if (empty($results) || empty($caseNumberInfo)) {
        return $results;
    }

    $filteredResults = [];
    $originalPattern = $caseNumberInfo['original'];
    $hasWildcard = $caseNumberInfo['hasWildcard'];
    $hasSuffix = $caseNumberInfo['hasSuffix'];

    foreach ($results as $dosar) {
        $caseNumber = $dosar->numar ?? '';
        $shouldInclude = false;

        if ($hasWildcard) {
            $basePattern = str_replace('*', '', $originalPattern);
            if (strpos($caseNumber, $basePattern) === 0) {
                $shouldInclude = true;
            }
        } elseif ($hasSuffix) {
            if ($caseNumber === $originalPattern) {
                $shouldInclude = true;
            }
        } else {
            if ($caseNumber === $originalPattern) {
                $shouldInclude = true;
            }
        }

        if ($shouldInclude) {
            $filteredResults[] = $dosar;
        }
    }

    return $filteredResults;
}

// Process the search exactly like index.php does
$searchTerms = trim($_POST['bulkSearchTerms'] ?? '');
$searchType = $_POST['searchType'] ?? 'auto';

echo "<h3>Search Parameters</h3>";
echo "<ul>";
echo "<li>Search Terms: '{$searchTerms}'</li>";
echo "<li>Search Type: '{$searchType}'</li>";
echo "</ul>";

if (!empty($searchTerms)) {
    // Parse search terms like index.php does
    $input = str_replace(',', "\n", $searchTerms);
    $terms = explode("\n", $input);
    $cleanTerms = [];

    foreach ($terms as $term) {
        $term = trim($term);
        if (!empty($term) && strlen($term) >= 2) {
            $cleanTerms[] = $term;
        }
    }
    echo "<h3>Parsed Terms</h3>";
    echo "<ul>";
    foreach ($cleanTerms as $index => $term) {
        echo "<li>Term " . ($index + 1) . ": '{$term}'</li>";
    }
    echo "</ul>";

    $allResults = [];
    $searchCriteria = [];

    foreach ($cleanTerms as $term) {
        $term = trim($term);
        if (empty($term)) continue;
        
        // Auto-detect search type for this term
        $detectedType = detectSearchType($term);
        echo "<h4>Processing term: '{$term}' (detected as: {$detectedType})</h4>";
        
        $searchParams = [
            'institutie' => null,
            'dataStart' => null,
            'dataStop' => null,
            'dataUltimaModificareStart' => null,
            'dataUltimaModificareStop' => null
        ];
        
        // Set the appropriate parameter based on detected type
        switch ($detectedType) {
            case 'numarDosar':
                $caseNumberInfo = normalizeCaseNumber($term);
                $searchParams['numarDosar'] = $caseNumberInfo['normalized'];
                $searchParams['_caseNumberInfo'] = $caseNumberInfo;
                
                echo "<h5>Case Number Info</h5>";
                echo "<ul>";
                echo "<li>Original: '{$caseNumberInfo['original']}'</li>";
                echo "<li>Normalized: '{$caseNumberInfo['normalized']}'</li>";
                echo "<li>Has wildcard: " . ($caseNumberInfo['hasWildcard'] ? 'YES' : 'NO') . "</li>";
                echo "<li>Has suffix: " . ($caseNumberInfo['hasSuffix'] ? 'YES' : 'NO') . "</li>";
                echo "</ul>";
                break;
            case 'obiectDosar':
                $searchParams['obiectDosar'] = $term;
                break;
            case 'numeParte':
            default:
                $searchParams['numeParte'] = $term;
                break;
        }
        
        try {
            // Use DosarService like index.php does
            $dosarService = new DosarService();
            $termResults = $dosarService->cautareAvansata($searchParams);

            // Apply client-side wildcard filtering for case numbers
            if (!empty($termResults) && $detectedType === 'numarDosar' && isset($searchParams['_caseNumberInfo'])) {
                $caseNumberInfo = $searchParams['_caseNumberInfo'];
                if ($caseNumberInfo['hasWildcard'] || $caseNumberInfo['hasSuffix']) {
                    echo "<p><strong>Applying client-side wildcard filtering...</strong></p>";
                    $originalCount = count($termResults);
                    $termResults = filterResultsByCaseNumberPattern($termResults, $caseNumberInfo);
                    $filteredCount = count($termResults);
                    echo "<p>Pattern filtering reduced results from {$originalCount} to {$filteredCount}</p>";
                }
            }
            
            echo "<h5>Results for term '{$term}'</h5>";
            echo "<p>Found " . count($termResults) . " cases</p>";
            
            if (count($termResults) > 0) {
                echo "<div class='table-responsive'>";
                echo "<table class='table table-striped table-sm'>";
                echo "<thead>";
                echo "<tr>";
                echo "<th>Număr</th>";
                echo "<th>Instanță</th>";
                echo "<th>Data</th>";
                echo "<th>Obiect</th>";
                echo "<th>Stadiu</th>";
                echo "</tr>";
                echo "</thead>";
                echo "<tbody>";
                
                foreach ($termResults as $dosar) {
                    $numarDosar = $dosar->numar ?? '';
                    $institutie = $dosar->institutie ?? '';
                    $dataDosar = $dosar->data ?? '';
                    $obiect = $dosar->obiect ?? '';
                    $stadiu = $dosar->stadiuProcesualNume ?? '';
                    
                    // Format date
                    $dataFormatted = '';
                    if (!empty($dataDosar)) {
                        try {
                            $date = new DateTime($dataDosar);
                            $dataFormatted = $date->format('d.m.Y');
                        } catch (Exception $e) {
                            $dataFormatted = $dataDosar;
                        }
                    }
                    
                    // Get institution name
                    $instituteName = '';
                    if (!empty($institutie)) {
                        $institutii = getInstanteList();
                        $instituteName = $institutii[$institutie] ?? $institutie;
                    }
                    
                    $rowClass = '';
                    if ($numarDosar === '14096/3/2024' && $institutie === 'TribunalulBUCURESTI') {
                        $rowClass = 'table-success';
                    }
                    
                    echo "<tr class='{$rowClass}'>";
                    echo "<td>" . htmlspecialchars($numarDosar) . "</td>";
                    echo "<td>" . htmlspecialchars($instituteName) . "</td>";
                    echo "<td>" . htmlspecialchars($dataFormatted) . "</td>";
                    echo "<td>" . htmlspecialchars(substr($obiect, 0, 50)) . "...</td>";
                    echo "<td>" . htmlspecialchars($stadiu) . "</td>";
                    echo "</tr>";
                }
                
                echo "</tbody>";
                echo "</table>";
                echo "</div>";
            }
            
            $allResults = array_merge($allResults, $termResults);
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>Error searching for term '{$term}': " . htmlspecialchars($e->getMessage()) . "</div>";
        }
    }

    echo "<h3>Final Summary</h3>";
    echo "<p>Total results across all terms: " . count($allResults) . "</p>";

    // Check if we found the expected case
    $foundExpectedCase = false;
    foreach ($allResults as $dosar) {
        if (($dosar->numar ?? '') === '14096/3/2024' && ($dosar->institutie ?? '') === 'TribunalulBUCURESTI') {
            $foundExpectedCase = true;
            break;
        }
    }

    if ($foundExpectedCase) {
        echo "<div class='alert alert-success'>";
        echo "<strong>✅ SUCCESS!</strong> The expected case '14096/3/2024' from 'TribunalulBUCURESTI' was found in the results.";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<strong>⚠️ WARNING!</strong> The expected case '14096/3/2024' from 'TribunalulBUCURESTI' was NOT found in the results.";
        echo "</div>";
    }

} else {
    echo "<div class='alert alert-info'>No search terms provided</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
