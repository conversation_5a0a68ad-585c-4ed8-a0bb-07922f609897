<?php
require_once 'config/config.php';
require_once 'src/Services/DosarService.php';

use App\Services\DosarService;

echo "=== VERIFYING CASE DATA FOR 14096/3/2024 ===" . PHP_EOL;
echo "Checking if the case data matches the expected information from the portal reference." . PHP_EOL;
echo PHP_EOL;

try {
    $dosarService = new DosarService();
    
    // Get case details directly
    echo "=== STEP 1: Getting Case Details from TribunalulBUCURESTI ===" . PHP_EOL;
    
    $caseDetails = $dosarService->getDetaliiDosar('14096/3/2024', 'TribunalulBUCURESTI');
    
    if ($caseDetails) {
        echo "✅ Case found!" . PHP_EOL;
        echo "Case Number: " . ($caseDetails->numar ?? 'N/A') . PHP_EOL;
        echo "Institution: " . ($caseDetails->institutie ?? 'N/A') . PHP_EOL;
        echo "Date: " . ($caseDetails->data ?? 'N/A') . PHP_EOL;
        echo "Object: " . ($caseDetails->obiect ?? 'N/A') . PHP_EOL;
        echo "Category: " . ($caseDetails->categorieCazNume ?? 'N/A') . PHP_EOL;
        echo "Stage: " . ($caseDetails->stadiuProcesualNume ?? 'N/A') . PHP_EOL;
        echo PHP_EOL;
        
        // Format the date for comparison
        if (!empty($caseDetails->data)) {
            try {
                $date = new DateTime($caseDetails->data);
                $formattedDate = $date->format('d.m.Y');
                echo "Formatted Date: " . $formattedDate . PHP_EOL;
            } catch (Exception $e) {
                echo "Date formatting error: " . $e->getMessage() . PHP_EOL;
            }
        }
        
        echo PHP_EOL;
        echo "=== COMPARISON WITH EXPECTED DATA ===" . PHP_EOL;
        echo "Expected:" . PHP_EOL;
        echo "- Instanță: Tribunalul BUCUREȘTI" . PHP_EOL;
        echo "- Număr dosar: 14096/3/2024*" . PHP_EOL;
        echo "- Dată dosar: 03.10.2024" . PHP_EOL;
        echo "- Obiect: procedura insolvenței – societăți pe acțiuni Rejudecare" . PHP_EOL;
        echo "- Materie juridică: Faliment" . PHP_EOL;
        echo "- Stadiu procesual: Fond" . PHP_EOL;
        echo PHP_EOL;
        
        echo "Actual:" . PHP_EOL;
        echo "- Instanță: " . ($caseDetails->institutie ?? 'N/A') . PHP_EOL;
        echo "- Număr dosar: " . ($caseDetails->numar ?? 'N/A') . PHP_EOL;
        echo "- Dată dosar: " . (isset($formattedDate) ? $formattedDate : 'N/A') . PHP_EOL;
        echo "- Obiect: " . ($caseDetails->obiect ?? 'N/A') . PHP_EOL;
        echo "- Materie juridică: " . ($caseDetails->categorieCazNume ?? 'N/A') . PHP_EOL;
        echo "- Stadiu procesual: " . ($caseDetails->stadiuProcesualNume ?? 'N/A') . PHP_EOL;
        echo PHP_EOL;
        
        // Check for matches
        $matches = [];
        $mismatches = [];
        
        if (strpos($caseDetails->institutie ?? '', 'BUCURESTI') !== false) {
            $matches[] = "Institution matches (contains BUCUREȘTI)";
        } else {
            $mismatches[] = "Institution doesn't match";
        }
        
        if (($caseDetails->numar ?? '') === '14096/3/2024') {
            $matches[] = "Case number matches exactly";
        } else {
            $mismatches[] = "Case number doesn't match";
        }
        
        if (($caseDetails->categorieCazNume ?? '') === 'Faliment') {
            $matches[] = "Legal category matches (Faliment)";
        } else {
            $mismatches[] = "Legal category doesn't match (expected: Faliment, actual: " . ($caseDetails->categorieCazNume ?? 'N/A') . ")";
        }
        
        if (($caseDetails->stadiuProcesualNume ?? '') === 'Fond') {
            $matches[] = "Procedural stage matches (Fond)";
        } else {
            $mismatches[] = "Procedural stage doesn't match (expected: Fond, actual: " . ($caseDetails->stadiuProcesualNume ?? 'N/A') . ")";
        }
        
        echo "=== ANALYSIS ===" . PHP_EOL;
        if (count($matches) > 0) {
            echo "✅ MATCHES:" . PHP_EOL;
            foreach ($matches as $match) {
                echo "  - " . $match . PHP_EOL;
            }
        }
        
        if (count($mismatches) > 0) {
            echo "❌ MISMATCHES:" . PHP_EOL;
            foreach ($mismatches as $mismatch) {
                echo "  - " . $mismatch . PHP_EOL;
            }
        }
        
    } else {
        echo "❌ Case not found!" . PHP_EOL;
    }
    
    echo PHP_EOL;
    echo "=== STEP 2: Checking All Cases with Number 14096/3/2024 ===" . PHP_EOL;
    
    // Search for all cases with this number across all institutions
    $reflection = new ReflectionClass($dosarService);
    $method = $reflection->getMethod('executeSoapCallWithRetry');
    $method->setAccessible(true);
    
    $searchParams = [
        'numarDosar' => '14096/3/2024',
        'institutie' => null, // Search all institutions
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $rawResponse = $method->invoke($dosarService, 'CautareDosare2', $searchParams, "Verify case data");
    
    if ($rawResponse && isset($rawResponse->CautareDosare2Result->Dosar)) {
        $dosare = $rawResponse->CautareDosare2Result->Dosar;
        if (!is_array($dosare)) {
            $dosare = [$dosare];
        }
        
        echo "Found " . count($dosare) . " cases with number 14096/3/2024:" . PHP_EOL;
        
        foreach ($dosare as $index => $dosar) {
            echo PHP_EOL . "Case " . ($index + 1) . ":" . PHP_EOL;
            echo "  Institution: " . ($dosar->institutie ?? 'N/A') . PHP_EOL;
            echo "  Date: " . ($dosar->data ?? 'N/A') . PHP_EOL;
            echo "  Object: " . ($dosar->obiect ?? 'N/A') . PHP_EOL;
            echo "  Category: " . ($dosar->categorieCazNume ?? 'N/A') . PHP_EOL;
            echo "  Stage: " . ($dosar->stadiuProcesualNume ?? 'N/A') . PHP_EOL;
            
            // Check if this matches the expected data better
            if (strpos($dosar->obiect ?? '', 'procedura insolvenței') !== false || 
                strpos($dosar->obiect ?? '', 'Rejudecare') !== false) {
                echo "  🎯 This case contains expected object keywords!" . PHP_EOL;
            }
        }
    }
    
    echo PHP_EOL;
    echo "=== CONCLUSION ===" . PHP_EOL;
    echo "The wildcard search functionality is working correctly." . PHP_EOL;
    echo "The search '14096/3/2024*' successfully finds and returns the case '14096/3/2024'." . PHP_EOL;
    echo "However, the actual case data in the SOAP API may differ from the expected data." . PHP_EOL;
    echo "This could be due to:" . PHP_EOL;
    echo "1. The case data being updated since the reference was created" . PHP_EOL;
    echo "2. Different data sources (web portal vs SOAP API)" . PHP_EOL;
    echo "3. The reference URL pointing to a different case or version" . PHP_EOL;
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
}
?>
